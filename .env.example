# API Hunter Environment Configuration Example
# 
# Copy this file to ~/api_hunt_envs/.api_hunt_env and fill in your actual values
# The tool will automatically create the ~/api_hunt_envs/ directory if it doesn't exist
#
# IMPORTANT: Never commit the actual .api_hunt_env file to version control!
# It should contain your real API keys and sensitive information.

# =============================================================================
# GOOGLE GEMINI API CONFIGURATION
# =============================================================================

# Google Gemini API Key for AI-powered regex pattern generation
# Get your API key from: https://makersuite.google.com/app/apikey
# This is optional - the tool works without it, but you won't be able to use
# the automatic regex generation feature (hunt -re command)
GOOGLE_API_KEY=your_google_gemini_api_key_here

# Example:
# GOOGLE_API_KEY=AIzaSyDXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================

# 1. Create the configuration directory (if it doesn't exist):
#    mkdir -p ~/api_hunt_envs/
#
# 2. Copy this file to the configuration directory:
#    cp .env.example ~/api_hunt_envs/.api_hunt_env
#
# 3. Edit the configuration file with your actual values:
#    nano ~/api_hunt_envs/.api_hunt_env
#    # or use your preferred text editor
#
# 4. Set your Gemini API key using the CLI (alternative method):
#    hunt -c "your_actual_api_key_here"
#
# 5. Verify the configuration:
#    hunt -d  # This should show any custom patterns you've added

# =============================================================================
# PRIVACY AND SECURITY NOTES
# =============================================================================

# - Your API keys are stored locally and never shared
# - When using the regex generation feature (hunt -re), API Hunter
#   anonymizes your actual API key before sending it to Gemini
# - Only the pattern structure is sent, not your real credentials
# - All configuration files are stored in your home directory (~/)
