# 📋 API Hunter - 配置文件总结

本文档总结了API Hunter项目的所有配置文件和设置要求。

## 🗂️ 配置文件清单

### ✅ 已存在的配置文件

| 文件名 | 位置 | 用途 | 状态 |
|--------|------|------|------|
| `pyproject.toml` | 项目根目录 | 项目元数据和依赖定义 | ✅ 已存在 |
| `setup.py` | 项目根目录 | 构建配置 | ✅ 已存在 |
| `.gitignore` | 项目根目录 | Git忽略规则 | ✅ 已存在 |
| `README.md` | 项目根目录 | 项目说明文档 | ✅ 已存在 |

### 🆕 新创建的配置文件

| 文件名 | 位置 | 用途 | 状态 |
|--------|------|------|------|
| `requirements.txt` | 项目根目录 | Python依赖列表 | 🆕 已创建 |
| `.env.example` | 项目根目录 | 环境变量示例文件 | 🆕 已创建 |
| `custom_pattern.example.json` | 项目根目录 | 自定义正则表达式模式示例 | 🆕 已创建 |
| `SETUP.md` | 项目根目录 | 详细设置指南 | 🆕 已创建 |
| `dev-setup.sh` | 项目根目录 | Linux/macOS自动设置脚本 | 🆕 已创建 |
| `dev-setup.bat` | 项目根目录 | Windows自动设置脚本 | 🆕 已创建 |
| `CONFIG_SUMMARY.md` | 项目根目录 | 配置文件总结（本文档） | 🆕 已创建 |

### 🔄 运行时创建的配置文件

这些文件在用户首次运行工具时自动创建：

| 文件名 | 位置 | 用途 | 创建时机 |
|--------|------|------|----------|
| `.api_hunt_env` | `~/api_hunt_envs/` | 实际的环境变量文件 | 用户配置API密钥时 |
| `custom_pattern.json` | `~/api_hunt_envs/` | 实际的自定义模式文件 | 用户添加自定义模式时 |

## 🔧 技术栈分析

### 核心技术
- **语言**: Python 3.7+
- **包管理**: setuptools + pip
- **异步处理**: asyncio
- **CLI框架**: argparse
- **AI集成**: Google Gemini API
- **版本控制**: Git集成

### 依赖关系
```
api_hunter/
├── google-genai (>= 0.3.0)    # Gemini AI集成
├── python-dotenv (>= 1.0.0)   # 环境变量管理
└── pydantic (>= 2.0.0)        # 数据验证
```

### 可选开发依赖
```
开发工具/
├── pytest (>= 7.0.0)          # 测试框架
├── pytest-asyncio (>= 0.21.0) # 异步测试支持
├── pytest-cov (>= 4.0.0)      # 测试覆盖率
├── black (>= 23.0.0)          # 代码格式化
├── flake8 (>= 6.0.0)          # 代码检查
└── mypy (>= 1.0.0)            # 类型检查
```

## 📁 目录结构

```
api-hunter/
├── 📁 api_hunt/                    # 主要源代码包
│   ├── 🐍 __init__.py
│   ├── 🐍 cli.py                   # 命令行接口
│   ├── 🐍 core.py                  # 核心扫描逻辑
│   ├── 🐍 patterns.py              # 内置正则表达式模式
│   ├── 🐍 add_delete.py            # 自定义模式管理
│   └── 🐍 get_repattern_ai.py      # AI集成功能
├── 📄 pyproject.toml               # 项目配置
├── 📄 setup.py                     # 构建配置
├── 📄 requirements.txt             # 依赖列表 🆕
├── 📄 .env.example                 # 环境变量示例 🆕
├── 📄 custom_pattern.example.json  # 自定义模式示例 🆕
├── 📄 SETUP.md                     # 设置指南 🆕
├── 📄 CONFIG_SUMMARY.md            # 配置总结 🆕
├── 🔧 dev-setup.sh                 # Linux/macOS设置脚本 🆕
├── 🔧 dev-setup.bat                # Windows设置脚本 🆕
├── 📄 README.md                    # 项目说明
└── 📄 .gitignore                   # Git忽略文件
```

## 🚀 快速启动流程

### 自动设置（推荐）

#### Linux/macOS:
```bash
chmod +x dev-setup.sh
./dev-setup.sh
```

#### Windows:
```cmd
dev-setup.bat
```

### 手动设置

1. **安装依赖**:
   ```bash
   pip install -r requirements.txt
   pip install -e .
   ```

2. **配置环境**:
   ```bash
   mkdir -p ~/api_hunt_envs/
   cp .env.example ~/api_hunt_envs/.api_hunt_env
   cp custom_pattern.example.json ~/api_hunt_envs/custom_pattern.json
   ```

3. **配置API密钥**（可选）:
   ```bash
   hunt -c "your_gemini_api_key"
   ```

4. **验证安装**:
   ```bash
   hunt --help
   ```

## 🔐 安全配置

### 敏感文件保护

以下文件包含敏感信息，已在`.gitignore`中排除：

- `~/api_hunt_envs/.api_hunt_env` - 包含API密钥
- `venv/` - 虚拟环境目录
- `*.pyc` - Python编译文件
- `__pycache__/` - Python缓存目录

### 环境变量安全

- ✅ API密钥存储在用户主目录下
- ✅ 使用`.env`文件管理敏感配置
- ✅ 提供示例文件但不包含真实密钥
- ✅ AI功能会自动匿名化API密钥

## 🔍 配置验证清单

在项目设置完成后，请验证以下项目：

- [ ] Python 3.7+ 已安装
- [ ] Git 已安装并可用
- [ ] 项目依赖已安装 (`pip list | grep -E "(google-genai|python-dotenv|pydantic)"`)
- [ ] `hunt` 命令可用 (`hunt --help`)
- [ ] 配置目录已创建 (`ls ~/api_hunt_envs/`)
- [ ] 环境变量文件已创建 (`ls ~/api_hunt_envs/.api_hunt_env`)
- [ ] 自定义模式文件已创建 (`ls ~/api_hunt_envs/custom_pattern.json`)
- [ ] Git仓库已初始化 (`git status`)

## 📞 故障排除

如果遇到问题，请按以下顺序检查：

1. **查看详细设置指南**: `SETUP.md`
2. **检查系统要求**: Python 3.7+, Git
3. **验证依赖安装**: `pip list`
4. **检查配置文件**: `~/api_hunt_envs/`
5. **查看错误日志**: 运行命令时的输出信息

## 🎯 下一步

配置完成后，您可以：

1. **开始使用**: `hunt -v`
2. **添加自定义模式**: `hunt -a "key_name" "pattern"`
3. **配置AI功能**: `hunt -c "api_key"`
4. **查看帮助**: `hunt --help`

---

**配置完成！开始保护您的代码安全吧！🔒**
