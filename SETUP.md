# 🚀 API Hunter - 完整设置指南

本文档提供了API Hunter项目的完整设置和配置说明，确保您能够在本地环境中成功运行该工具。

## 📋 目录

- [项目概述](#项目概述)
- [系统要求](#系统要求)
- [快速开始](#快速开始)
- [详细安装步骤](#详细安装步骤)
- [配置说明](#配置说明)
- [使用示例](#使用示例)
- [故障排除](#故障排除)
- [开发环境设置](#开发环境设置)

## 🎯 项目概述

**API Hunter** 是一个轻量级的Python命令行工具，用于扫描Git暂存文件中的敏感凭据（如API密钥、访问令牌、私钥等）。

### 核心功能
- ✅ 扫描Git暂存文件中的敏感信息
- 🔍 支持多种API密钥格式检测
- ⚡️ 异步扫描，性能优异
- 🛠️ 自定义正则表达式模式
- 🤖 Gemini AI集成，自动生成正则表达式
- 🎨 彩色输出，易于阅读

### 技术栈
- **语言**: Python 3.7+
- **核心依赖**: google-genai, python-dotenv, pydantic
- **构建工具**: setuptools
- **版本控制**: Git集成

## 💻 系统要求

### 必需组件
- **Python**: 3.7 或更高版本
- **Git**: 任何现代版本
- **操作系统**: Windows, macOS, Linux

### 可选组件
- **Google Gemini API密钥**: 用于AI驱动的正则表达式生成功能

## ⚡ 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd api-hunter
```

### 2. 安装依赖
```bash
# 使用pip安装
pip install -r requirements.txt

# 或者使用项目配置安装
pip install -e .
```

### 3. 基本使用
```bash
# 扫描当前Git仓库的暂存文件
hunt

# 扫描特定文件
hunt -n path/to/your/file.py
```

## 🔧 详细安装步骤

### 步骤1: 环境准备

#### 检查Python版本
```bash
python --version
# 应该显示 Python 3.7.x 或更高版本
```

#### 检查Git安装
```bash
git --version
# 应该显示Git版本信息
```

### 步骤2: 项目安装

#### 方法A: 开发模式安装（推荐）
```bash
# 克隆项目
git clone <repository-url>
cd api-hunter

# 创建虚拟环境（推荐）
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 以开发模式安装项目
pip install -e .
```

#### 方法B: 直接安装
```bash
pip install -r requirements.txt
```

### 步骤3: 验证安装
```bash
# 检查命令是否可用
hunt --help

# 应该显示帮助信息
```

## ⚙️ 配置说明

### 环境变量配置

API Hunter使用环境变量文件存储配置信息。

#### 1. 创建配置目录
```bash
mkdir -p ~/api_hunt_envs/
```

#### 2. 创建环境变量文件
```bash
# 复制示例文件
cp .env.example ~/api_hunt_envs/.api_hunt_env

# 编辑配置文件
nano ~/api_hunt_envs/.api_hunt_env
```

#### 3. 配置Gemini API密钥（可选）
```bash
# 方法1: 直接编辑配置文件
# 在 ~/api_hunt_envs/.api_hunt_env 中设置:
# GOOGLE_API_KEY=your_actual_api_key_here

# 方法2: 使用CLI命令
hunt -c "your_actual_api_key_here"
```

### 自定义模式配置

#### 1. 创建自定义模式文件
```bash
# 复制示例文件
cp custom_pattern.example.json ~/api_hunt_envs/custom_pattern.json
```

#### 2. 编辑自定义模式
```bash
nano ~/api_hunt_envs/custom_pattern.json
```

#### 3. 使用CLI添加模式
```bash
# 添加自定义模式
hunt -a "my_service" "my_service_[a-zA-Z0-9]{32}"

# 查看所有自定义模式
hunt -d

# 删除模式
hunt -r "my_service"
```

## 📚 使用示例

### 基本扫描
```bash
# 扫描所有暂存文件
hunt

# 详细输出（显示匹配的模式）
hunt -v

# 扫描特定文件
hunt -n src/config.py

# 扫描特定文件（详细输出）
hunt -n src/config.py -v
```

### 自定义模式管理
```bash
# 添加新的API密钥模式
hunt -a "stripe_key" "sk_(live|test)_[0-9a-zA-Z]{24}"

# 查看所有自定义模式
hunt -d

# 删除模式
hunt -r "stripe_key"
```

### AI辅助模式生成
```bash
# 配置Gemini API密钥
hunt -c "your_gemini_api_key"

# 使用AI生成并添加正则表达式
hunt -re "my_service" "sk-abc123def456..."
# 注意: 工具会自动匿名化您的API密钥再发送给AI
```

## 🔍 故障排除

### 常见问题

#### 1. 命令未找到
```bash
# 错误: hunt: command not found
# 解决方案:
pip install -e .
# 或者检查PATH环境变量
```

#### 2. 没有找到暂存文件
```bash
# 错误: no files found in index please git add to add files
# 解决方案:
git add your_files
hunt
```

#### 3. 不是Git仓库
```bash
# 错误: No git found please intialize a git Repo
# 解决方案:
git init
git add .
hunt
```

#### 4. Gemini API错误
```bash
# 错误: error getting gemini api key please check if gemini api key is configured
# 解决方案:
hunt -c "your_valid_api_key"
```

#### 5. 权限错误
```bash
# 错误: Permission denied when creating ~/api_hunt_envs/
# 解决方案:
sudo mkdir -p ~/api_hunt_envs/
sudo chown $USER:$USER ~/api_hunt_envs/
```

### 调试技巧

#### 1. 检查配置文件
```bash
# 检查环境变量文件
cat ~/api_hunt_envs/.api_hunt_env

# 检查自定义模式文件
cat ~/api_hunt_envs/custom_pattern.json
```

#### 2. 验证Git状态
```bash
# 检查暂存文件
git status

# 查看暂存的文件列表
git diff --cached --name-only
```

#### 3. 测试特定文件
```bash
# 创建测试文件
echo 'API_KEY="sk-test123456789"' > test_file.py
git add test_file.py
hunt -v
```

## 🛠️ 开发环境设置

### 开发依赖安装
```bash
# 安装开发依赖（取消requirements.txt中的注释）
pip install pytest pytest-asyncio pytest-cov
pip install black flake8 mypy
```

### 代码质量检查
```bash
# 代码格式化
black api_hunt/

# 代码检查
flake8 api_hunt/

# 类型检查
mypy api_hunt/
```

### 运行测试
```bash
# 运行所有测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=api_hunt
```

### 构建和发布
```bash
# 构建包
python -m build

# 检查包
twine check dist/*

# 发布到PyPI（需要配置凭据）
twine upload dist/*
```

## 📁 项目结构

```
api-hunter/
├── api_hunt/                 # 主要源代码
│   ├── __init__.py
│   ├── cli.py               # 命令行接口
│   ├── core.py              # 核心扫描逻辑
│   ├── patterns.py          # 内置正则表达式模式
│   ├── add_delete.py        # 自定义模式管理
│   └── get_repattern_ai.py  # AI集成功能
├── pyproject.toml           # 项目配置
├── setup.py                 # 构建配置
├── requirements.txt         # 依赖列表
├── .env.example             # 环境变量示例
├── custom_pattern.example.json  # 自定义模式示例
├── SETUP.md                 # 本设置指南
├── README.md                # 项目说明
└── .gitignore              # Git忽略文件
```

## 🔗 相关链接

- [Google Gemini API](https://makersuite.google.com/app/apikey) - 获取API密钥
- [Python官网](https://www.python.org/) - Python下载和文档
- [Git官网](https://git-scm.com/) - Git下载和文档

## 📞 支持

如果您遇到问题或需要帮助，请：

1. 查看本设置指南的故障排除部分
2. 检查项目的README.md文件
3. 在项目仓库中创建Issue

---

**祝您使用愉快！🎉**
