@echo off
REM API Hunter - Windows开发环境自动设置脚本
REM 
REM 此脚本自动设置API Hunter的开发环境
REM 使用方法: 双击运行或在命令行中执行 dev-setup.bat

setlocal enabledelayedexpansion

echo 🚀 API Hunter Windows开发环境设置脚本
echo ==========================================

REM 检查Python是否安装
echo [INFO] 检查Python版本...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] 未找到Python。请安装Python 3.7或更高版本。
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [SUCCESS] 找到Python版本: %PYTHON_VERSION%

REM 检查Git是否安装
echo [INFO] 检查Git...
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] 未找到Git。请安装Git。
    echo 下载地址: https://git-scm.com/download/win
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('git --version') do set GIT_VERSION=%%i
echo [SUCCESS] 找到Git: %GIT_VERSION%

REM 创建虚拟环境
echo [INFO] 创建Python虚拟环境...
if exist "venv" (
    echo [WARNING] 虚拟环境已存在，跳过创建。
) else (
    python -m venv venv
    if %errorlevel% neq 0 (
        echo [ERROR] 创建虚拟环境失败。
        pause
        exit /b 1
    )
    echo [SUCCESS] 虚拟环境创建成功。
)

REM 激活虚拟环境
echo [INFO] 激活虚拟环境...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo [ERROR] 激活虚拟环境失败。
    pause
    exit /b 1
)

REM 升级pip
echo [INFO] 升级pip...
python -m pip install --upgrade pip

REM 安装依赖
echo [INFO] 安装项目依赖...
if exist "requirements.txt" (
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo [ERROR] 安装依赖失败。
        pause
        exit /b 1
    )
    echo [SUCCESS] 依赖安装完成。
) else (
    echo [ERROR] 未找到requirements.txt文件。
    pause
    exit /b 1
)

REM 以开发模式安装项目
echo [INFO] 以开发模式安装项目...
pip install -e .
if %errorlevel% neq 0 (
    echo [ERROR] 项目安装失败。
    pause
    exit /b 1
)
echo [SUCCESS] 项目以开发模式安装完成。

REM 创建配置目录
echo [INFO] 设置配置文件...
set CONFIG_DIR=%USERPROFILE%\api_hunt_envs
if not exist "%CONFIG_DIR%" (
    mkdir "%CONFIG_DIR%"
)
echo [SUCCESS] 配置目录创建: %CONFIG_DIR%

REM 复制配置文件
if exist ".env.example" (
    if not exist "%CONFIG_DIR%\.api_hunt_env" (
        copy ".env.example" "%CONFIG_DIR%\.api_hunt_env" >nul
        echo [SUCCESS] 环境变量文件创建: %CONFIG_DIR%\.api_hunt_env
        echo [WARNING] 请编辑 %CONFIG_DIR%\.api_hunt_env 文件，添加您的Gemini API密钥。
    )
)

if exist "custom_pattern.example.json" (
    if not exist "%CONFIG_DIR%\custom_pattern.json" (
        copy "custom_pattern.example.json" "%CONFIG_DIR%\custom_pattern.json" >nul
        echo [SUCCESS] 自定义模式文件创建: %CONFIG_DIR%\custom_pattern.json
    )
)

REM 初始化Git仓库（如果需要）
if not exist ".git" (
    echo [INFO] 初始化Git仓库...
    git init
    echo [SUCCESS] Git仓库初始化完成。
) else (
    echo [SUCCESS] Git仓库已存在。
)

REM 验证安装
echo [INFO] 验证安装...
hunt --help >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] hunt命令不可用。安装可能失败。
    pause
    exit /b 1
)
echo [SUCCESS] hunt命令可用。

echo.
echo [SUCCESS] 🎉 开发环境设置完成！
echo.
echo [INFO] 下一步操作:
echo 1. 编辑配置文件: notepad %CONFIG_DIR%\.api_hunt_env
echo 2. 添加一些文件到Git: git add .
echo 3. 运行扫描: hunt -v
echo.
echo [INFO] 激活虚拟环境:
echo venv\Scripts\activate.bat
echo.
echo 按任意键退出...
pause >nul
