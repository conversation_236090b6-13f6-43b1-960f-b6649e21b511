#!/bin/bash

# API Hunter - 开发环境自动设置脚本
# 
# 此脚本自动设置API Hunter的开发环境
# 使用方法: chmod +x dev-setup.sh && ./dev-setup.sh

set -e  # 遇到错误时退出

echo "🚀 API Hunter 开发环境设置脚本"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色消息
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查Python版本
check_python() {
    print_status "检查Python版本..."
    
    if command_exists python3; then
        PYTHON_CMD="python3"
    elif command_exists python; then
        PYTHON_CMD="python"
    else
        print_error "未找到Python。请安装Python 3.7或更高版本。"
        exit 1
    fi
    
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    print_success "找到Python版本: $PYTHON_VERSION"
    
    # 检查版本是否满足要求
    if $PYTHON_CMD -c "import sys; exit(0 if sys.version_info >= (3, 7) else 1)"; then
        print_success "Python版本满足要求 (>= 3.7)"
    else
        print_error "Python版本过低。需要Python 3.7或更高版本。"
        exit 1
    fi
}

# 检查Git
check_git() {
    print_status "检查Git..."
    
    if command_exists git; then
        GIT_VERSION=$(git --version)
        print_success "找到Git: $GIT_VERSION"
    else
        print_error "未找到Git。请安装Git。"
        exit 1
    fi
}

# 创建虚拟环境
create_venv() {
    print_status "创建Python虚拟环境..."
    
    if [ -d "venv" ]; then
        print_warning "虚拟环境已存在，跳过创建。"
    else
        $PYTHON_CMD -m venv venv
        print_success "虚拟环境创建成功。"
    fi
}

# 激活虚拟环境并安装依赖
install_dependencies() {
    print_status "激活虚拟环境并安装依赖..."
    
    # 激活虚拟环境
    if [ -f "venv/bin/activate" ]; then
        source venv/bin/activate
    elif [ -f "venv/Scripts/activate" ]; then
        source venv/Scripts/activate
    else
        print_error "无法找到虚拟环境激活脚本。"
        exit 1
    fi
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装依赖
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
        print_success "依赖安装完成。"
    else
        print_error "未找到requirements.txt文件。"
        exit 1
    fi
    
    # 以开发模式安装项目
    pip install -e .
    print_success "项目以开发模式安装完成。"
}

# 创建配置目录和文件
setup_config() {
    print_status "设置配置文件..."
    
    # 创建配置目录
    CONFIG_DIR="$HOME/api_hunt_envs"
    mkdir -p "$CONFIG_DIR"
    print_success "配置目录创建: $CONFIG_DIR"
    
    # 复制环境变量示例文件
    if [ -f ".env.example" ] && [ ! -f "$CONFIG_DIR/.api_hunt_env" ]; then
        cp .env.example "$CONFIG_DIR/.api_hunt_env"
        print_success "环境变量文件创建: $CONFIG_DIR/.api_hunt_env"
        print_warning "请编辑 $CONFIG_DIR/.api_hunt_env 文件，添加您的Gemini API密钥。"
    fi
    
    # 复制自定义模式示例文件
    if [ -f "custom_pattern.example.json" ] && [ ! -f "$CONFIG_DIR/custom_pattern.json" ]; then
        cp custom_pattern.example.json "$CONFIG_DIR/custom_pattern.json"
        print_success "自定义模式文件创建: $CONFIG_DIR/custom_pattern.json"
    fi
}

# 验证安装
verify_installation() {
    print_status "验证安装..."
    
    # 检查命令是否可用
    if command_exists hunt; then
        print_success "hunt命令可用。"
        
        # 显示帮助信息
        print_status "显示帮助信息:"
        hunt --help
    else
        print_error "hunt命令不可用。安装可能失败。"
        exit 1
    fi
}

# 初始化Git仓库（如果需要）
init_git_if_needed() {
    if [ ! -d ".git" ]; then
        print_status "初始化Git仓库..."
        git init
        print_success "Git仓库初始化完成。"
    else
        print_success "Git仓库已存在。"
    fi
}

# 主函数
main() {
    echo
    print_status "开始设置开发环境..."
    echo
    
    # 执行各个步骤
    check_python
    check_git
    create_venv
    install_dependencies
    setup_config
    init_git_if_needed
    verify_installation
    
    echo
    print_success "🎉 开发环境设置完成！"
    echo
    print_status "下一步操作:"
    echo "1. 编辑配置文件: nano $HOME/api_hunt_envs/.api_hunt_env"
    echo "2. 添加一些文件到Git: git add ."
    echo "3. 运行扫描: hunt -v"
    echo
    print_status "激活虚拟环境:"
    echo "source venv/bin/activate  # Linux/macOS"
    echo "venv\\Scripts\\activate     # Windows"
    echo
}

# 运行主函数
main "$@"
