[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "api_hunter"
version = "0.1.2"
description = "Scan staged Git files for secrets like API keys."
readme = "README.md"
requires-python = ">=3.7"
dependencies = [
    "google-genai",
    "python-dotenv", 
    "pydantic"
]
authors = [
    {name = "<PERSON> Mama", email = "<EMAIL>"}
]
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
]

[project.scripts]
hunt = "api_hunt.cli:main" 