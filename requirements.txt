# API Hunter - Python Dependencies
# 
# This file lists all Python packages required to run API Hunter
# Install with: pip install -r requirements.txt

# Core dependencies (from pyproject.toml)
google-genai>=0.3.0
python-dotenv>=1.0.0
pydantic>=2.0.0

# Development dependencies (optional)
# Uncomment the following lines if you plan to contribute to the project

# Testing
# pytest>=7.0.0
# pytest-asyncio>=0.21.0
# pytest-cov>=4.0.0

# Code quality
# black>=23.0.0
# flake8>=6.0.0
# mypy>=1.0.0

# Documentation
# sphinx>=6.0.0
# sphinx-rtd-theme>=1.2.0

# Build tools
# build>=0.10.0
# twine>=4.0.0
